{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "Custom API - Spring Boot", "description": "Complete API collection for testing the Spring Boot Custom API with PostgreSQL", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health & Status", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}, "description": "Check if the API is running and healthy"}}, {"name": "Welcome Message", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/welcome", "host": ["{{base_url}}"], "path": ["api", "welcome"]}, "description": "Get welcome message from the API"}}]}, {"name": "User Management", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}, "description": "Retrieve all users from the database"}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users/1", "host": ["{{base_url}}"], "path": ["api", "users", "1"]}, "description": "Get a specific user by their ID"}}, {"name": "Get User by Email", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users/email/<EMAIL>", "host": ["{{base_url}}"], "path": ["api", "users", "email", "<EMAIL>"]}, "description": "Find a user by their email address"}}, {"name": "Search Users by Name", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users/search?name=John", "host": ["{{base_url}}"], "path": ["api", "users", "search"], "query": [{"key": "name", "value": "<PERSON>"}]}, "description": "Search for users by name (case insensitive)"}}, {"name": "Create New User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"******-0999\",\n    \"address\": \"999 Elm Street, New City, USA\"\n}"}, "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}, "description": "Create a new user in the system"}}, {"name": "Create User <PERSON> <PERSON><PERSON><PERSON><PERSON> (Special Greeting)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+91-9876543210\",\n    \"address\": \"123 Mumbai Street, Maharashtra, India\"\n}"}, "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}, "description": "Create a user named exactly '<PERSON><PERSON><PERSON><PERSON>' - triggers special greeting after database save"}}, {"name": "Create User <PERSON> <PERSON><PERSON><PERSON><PERSON> (No Special Greeting)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"prathamesh\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+91-9876543211\",\n    \"address\": \"456 Delhi Street, Delhi, India\"\n}"}, "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}, "description": "Create a user named 'p<PERSON><PERSON><PERSON>' (lowercase) - NO special greeting (exact match required)"}}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"******-0111\",\n    \"address\": \"111 Updated Street, Modified City, USA\"\n}"}, "url": {"raw": "{{base_url}}/api/users/1", "host": ["{{base_url}}"], "path": ["api", "users", "1"]}, "description": "Update an existing user's information"}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/users/1", "host": ["{{base_url}}"], "path": ["api", "users", "1"]}, "description": "Delete a user from the system"}}]}, {"name": "User Statistics", "item": [{"name": "Get User Count", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users/count", "host": ["{{base_url}}"], "path": ["api", "users", "count"]}, "description": "Get the total number of users in the system"}}, {"name": "Check if User Exists", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users/exists/1", "host": ["{{base_url}}"], "path": ["api", "users", "exists", "1"]}, "description": "Check if a user with the given ID exists"}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}]}