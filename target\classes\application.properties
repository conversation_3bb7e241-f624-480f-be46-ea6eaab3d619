# Server Configuration
server.port=8080
server.servlet.context-path=/

# Application Configuration
spring.application.name=custom-api

# PostgreSQL Database Configuration
spring.datasource.url=*****************************************
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.username=postgres
spring.datasource.password=postgres

# JPA/Hibernate Configuration
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Logging Configuration
logging.level.com.example.customapi=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG

# JSON Configuration
spring.jackson.serialization.indent-output=true

# Validation Configuration
spring.jpa.properties.hibernate.validator.apply_to_ddl=false
