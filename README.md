# Custom API - Spring Boot REST API

A Spring Boot REST API for user management with PostgreSQL database integration.

## Prerequisites

1. **Java 17** or higher
2. **Maven 3.6+**
3. **PostgreSQL 12+**

## Database Setup

### 1. Install PostgreSQL
Download and install PostgreSQL from [https://www.postgresql.org/download/](https://www.postgresql.org/download/)

### 2. Create Database
```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database
CREATE DATABASE customapi;

-- Create user (optional)
CREATE USER apiuser WITH PASSWORD 'password';
GRANT ALL PRIVILEGES ON DATABASE customapi TO apiuser;
```

### 3. Update Database Configuration
Edit `src/main/resources/application.properties` if needed:
```properties
spring.datasource.url=******************************************
spring.datasource.username=postgres
spring.datasource.password=password
```

## Running the Application

1. **Clone/Navigate to project directory**
2. **Install dependencies:**
   ```bash
   mvn clean install
   ```
3. **Run the application:**
   ```bash
   mvn spring-boot:run
   ```

The API will be available at: `http://localhost:8080`

## API Endpoints

### Health Check
- `GET /api/health` - Check API status
- `GET /api/welcome` - Welcome message

### User Management
- `GET /api/users` - Get all users
- `GET /api/users/{id}` - Get user by ID
- `GET /api/users/email/{email}` - Get user by email
- `GET /api/users/search?name={name}` - Search users by name
- `POST /api/users` - Create new user
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user
- `GET /api/users/count` - Get total user count
- `GET /api/users/exists/{id}` - Check if user exists

## Sample API Requests for Postman

### 1. Health Check
```
GET http://localhost:8080/api/health
```

### 2. Get All Users
```
GET http://localhost:8080/api/users
```

### 3. Create User
```
POST http://localhost:8080/api/users
Content-Type: application/json

{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "******-0123",
    "address": "123 Main St, Anytown, USA"
}
```

### 4. Update User
```
PUT http://localhost:8080/api/users/1
Content-Type: application/json

{
    "name": "John Smith",
    "email": "<EMAIL>",
    "phone": "******-0124",
    "address": "124 Main St, Anytown, USA"
}
```

### 5. Delete User
```
DELETE http://localhost:8080/api/users/1
```

## Features

- ✅ RESTful API design
- ✅ PostgreSQL database integration
- ✅ Data validation
- ✅ Error handling
- ✅ CORS enabled for testing
- ✅ Sample data initialization
- ✅ Comprehensive CRUD operations

## Technologies Used

- Spring Boot 3.2.0
- Spring Data JPA
- PostgreSQL
- Maven
- Java 17
