# Server Configuration
server.port=8080
server.servlet.context-path=/

# Application Configuration
spring.application.name=custom-api

# H2 Database Configuration (In-memory for development)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# H2 Console (for database inspection)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA/Hibernate Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Logging Configuration
logging.level.com.example.customapi=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG

# JSON Configuration
spring.jackson.serialization.indent-output=true

# Validation Configuration
spring.jpa.properties.hibernate.validator.apply_to_ddl=false
