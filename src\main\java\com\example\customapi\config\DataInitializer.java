package com.example.customapi.config;

import com.example.customapi.model.User;
import com.example.customapi.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;

    @Override
    public void run(String... args) throws Exception {
        // Initialize with some sample data
        if (userRepository.count() == 0) {
            User user1 = new User("<PERSON>", "<EMAIL>", "+1-555-0123", "123 Main St, Anytown, USA");
            User user2 = new User("<PERSON>", "<EMAIL>", "+1-555-0456", "456 Oak Ave, Somewhere, USA");
            User user3 = new User("<PERSON>", "<EMAIL>", "+1-555-0789", "789 Pine Rd, Elsewhere, USA");

            userRepository.save(user1);
            userRepository.save(user2);
            userRepository.save(user3);

            System.out.println("Sample data initialized!");
        }
    }
}
